import { type Schemas } from "@/types";
import { type PageProps } from "../../Common/Header/WizardHeader";
import { type PageName } from "../LeadContractWizard";
import {
  Button,
  Paper,
  Stack,
  Text,
  Group,
  Checkbox,
  Table,
  Badge,
  NumberInput,
} from "@mantine/core";
import { useState } from "react";
import { IconPlus } from "@tabler/icons-react";

interface PageAccessoriesProps extends PageProps<PageName> {
  lead?: Schemas["LeadRetrieveDto"];
}

interface AccessoryItem {
  id: string;
  name: string;
  price: number;
  category: string;
  inStock: number;
}

interface SelectedAccessory extends AccessoryItem {
  quantity: number;
}

const mockAccessories: AccessoryItem[] = [
  {
    id: "1",
    name: "Padlock",
    price: 15.0,
    category: "Security",
    inStock: 50,
  },
  {
    id: "2",
    name: "Storage Box",
    price: 25.0,
    category: "Storage",
    inStock: 30,
  },
  {
    id: "3",
    name: "Climate Control",
    price: 45.0,
    category: "Climate",
    inStock: 20,
  },
  {
    id: "4",
    name: "Shelving Unit",
    price: 35.0,
    category: "Storage",
    inStock: 15,
  },
];

export default function PageAccessories({
  lead: _lead,
  setPages,
  pages,
}: PageAccessoriesProps) {
  const [selectedAccessories, setSelectedAccessories] = useState<
    SelectedAccessory[]
  >([]);

  const handleAccessoryToggle = (
    accessory: AccessoryItem,
    checked: boolean,
  ) => {
    if (checked) {
      setSelectedAccessories((prev) => [
        ...prev,
        { ...accessory, quantity: 1 },
      ]);
    } else {
      setSelectedAccessories((prev) =>
        prev.filter((item) => item.id !== accessory.id),
      );
    }
  };

  const handleQuantityChange = (accessoryId: string, quantity: number) => {
    setSelectedAccessories((prev) =>
      prev.map((item) =>
        item.id === accessoryId
          ? { ...item, quantity: Math.max(1, quantity) }
          : item,
      ),
    );
  };

  const isSelected = (accessoryId: string) => {
    return selectedAccessories.some((item) => item.id === accessoryId);
  };

  const getQuantity = (accessoryId: string) => {
    const item = selectedAccessories.find((item) => item.id === accessoryId);
    return item?.quantity || 1;
  };

  const handleNext = () => {
    setPages([...pages, "DISCOUNT"]);
  };

  const totalCost = selectedAccessories.reduce(
    (sum, item) => sum + item.price * item.quantity,
    0,
  );

  return (
    <Paper p="xl" radius="md" style={{ height: "100%", overflow: "auto" }}>
      <Stack gap="lg">
        <Table>
          <Table.Thead>
            <Table.Tr>
              <Table.Th>Select</Table.Th>
              <Table.Th>Accessory</Table.Th>
              <Table.Th>Category</Table.Th>
              <Table.Th>Price</Table.Th>
              <Table.Th>Stock</Table.Th>
              <Table.Th>Quantity</Table.Th>
            </Table.Tr>
          </Table.Thead>
          <Table.Tbody>
            {mockAccessories.map((accessory) => (
              <Table.Tr key={accessory.id}>
                <Table.Td>
                  <Checkbox
                    checked={isSelected(accessory.id)}
                    onChange={(event) =>
                      handleAccessoryToggle(
                        accessory,
                        event.currentTarget.checked,
                      )
                    }
                  />
                </Table.Td>
                <Table.Td>
                  <Text fw={500}>{accessory.name}</Text>
                </Table.Td>
                <Table.Td>
                  <Badge variant="light" size="sm">
                    {accessory.category}
                  </Badge>
                </Table.Td>
                <Table.Td>
                  <Text>€{accessory.price.toFixed(2)}</Text>
                </Table.Td>
                <Table.Td>
                  <Text
                    size="sm"
                    c={accessory.inStock > 10 ? "green" : "orange"}
                  >
                    {accessory.inStock} in stock
                  </Text>
                </Table.Td>
                <Table.Td>
                  {isSelected(accessory.id) && (
                    <NumberInput
                      value={getQuantity(accessory.id)}
                      onChange={(value) =>
                        handleQuantityChange(accessory.id, Number(value) || 1)
                      }
                      min={1}
                      max={accessory.inStock}
                      size="sm"
                      style={{ width: 80 }}
                    />
                  )}
                </Table.Td>
              </Table.Tr>
            ))}
          </Table.Tbody>
        </Table>

        {selectedAccessories.length > 0 && (
          <Paper p="md" withBorder>
            <Group justify="space-between">
              <Text fw={500}>
                Selected accessories ({selectedAccessories.length})
              </Text>
              <Text fw={600} size="lg">
                Total: €{totalCost.toFixed(2)}
              </Text>
            </Group>
          </Paper>
        )}

        <Group justify="end" mt="xl">
          <Button leftSection={<IconPlus />} onClick={handleNext}>
            Add selected tags
          </Button>
        </Group>
      </Stack>
    </Paper>
  );
}
