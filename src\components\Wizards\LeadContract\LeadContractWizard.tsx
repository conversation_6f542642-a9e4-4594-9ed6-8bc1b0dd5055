import { Box, Loader } from "@mantine/core";
import { useState } from "react";
import WizardHeader from "../Common/Header/WizardHeader";
import { type Schemas } from "@/types";
import PageStart from "./Structure/PageStart";
import PageUnitSelection from "./Structure/PageUnitSelection";
import PageUnitSearch from "./Structure/PageUnitSearch";
import PageUnitBrowse from "./Structure/PageUnitBrowse";
import PageAccessories from "./Structure/PageAccessories";
import PageDiscount from "./Structure/PageDiscount";
import PageProducts from "./Structure/PageProducts";
import PageAddProduct from "./Structure/PageAddProduct";
import PageSignContract from "./Structure/PageSignContract";
import PageVerification from "./Structure/PageVerification";
import PageNfcStorageLogic from "./Structure/PageNfcStorageLogic";
import PageManualVerification from "./Structure/PageManualVerification";
import PageIdinVerification from "./Structure/PageIdinVerification";
import { useEntityQuery } from "@/features/entity/queries";

interface LeadContractWizardProps {
  closeModal?: () => void;
  lead: Schemas["LeadRetrieveDto"];
  activeWizard: string | null;
}

const DEFAULT_TOTAL_PAGES = 3;

export type PageName =
  | "VERIFICATION"
  | "START"
  | "IDENTIFICATION"
  | "UNIT_SELECTION"
  | "UNIT_SEARCH"
  | "UNIT_BROWSE"
  | "ACCESSORIES"
  | "DISCOUNT"
  | "PRODUCTS"
  | "ADD_PRODUCT"
  | "SIGN_CONTRACT"
  | "NFC_STORAGE_LOGIC"
  | "MANUAL_VERIFICATION"
  | "IDIN_VERIFICATION"
  | "COMPLETED";

export default function LeadContractWizard({
  closeModal,
  lead,
  activeWizard: activeWizardId,
}: LeadContractWizardProps) {
  const [pages, setPages] = useState<PageName[]>(["UNIT_SELECTION"]);
  const [totalPages, setTotalPages] = useState(DEFAULT_TOTAL_PAGES);
  const { data, isLoading } = useEntityQuery<Schemas["ContractRetrieveDto"]>({
    resourcePath: `/api/Contracts/{id}`,
    resourceId: activeWizardId!,
    queryKey: "contract",
  });

  if (isLoading) {
    <Box h={"100%"}>
      <Loader />
    </Box>;
  }

  const currentWizard: Schemas["ContractRetrieveDto"] | undefined = data;
  const currentPageContent = () => {
    switch (pages.at(-1)) {
      case "VERIFICATION":
        return (
          <PageVerification
            setPages={setPages}
            pages={pages}
            setTotalPages={setTotalPages}
          />
        );
      case "START":
        return (
          <PageStart
            setPages={setPages}
            setTotalPages={setTotalPages}
            pages={pages}
            lead={lead}
          />
        );
      case "UNIT_SELECTION":
        return (
          <PageUnitSelection
            lead={lead}
            setPages={setPages}
            pages={pages}
            currentWizard={currentWizard}
            setTotalPages={setTotalPages}
          />
        );
      case "UNIT_SEARCH":
        return (
          <PageUnitSearch
            lead={lead}
            setPages={setPages}
            pages={pages}
            setTotalPages={setTotalPages}
          />
        );
      case "UNIT_BROWSE":
        return (
          <PageUnitBrowse
            lead={lead}
            setPages={setPages}
            pages={pages}
            setTotalPages={setTotalPages}
          />
        );
      case "ACCESSORIES":
        return (
          <PageAccessories
            lead={lead}
            setPages={setPages}
            pages={pages}
            setTotalPages={setTotalPages}
          />
        );
      case "DISCOUNT":
        return (
          <PageDiscount
            lead={lead}
            setPages={setPages}
            pages={pages}
            setTotalPages={setTotalPages}
          />
        );
      case "PRODUCTS":
        return (
          <PageProducts
            lead={lead}
            setPages={setPages}
            pages={pages}
            setTotalPages={setTotalPages}
          />
        );
      case "ADD_PRODUCT":
        return (
          <PageAddProduct
            lead={lead}
            setPages={setPages}
            pages={pages}
            setTotalPages={setTotalPages}
          />
        );
      case "SIGN_CONTRACT":
        return (
          <PageSignContract
            lead={lead}
            setPages={setPages}
            pages={pages}
            setTotalPages={setTotalPages}
          />
        );
      case "NFC_STORAGE_LOGIC":
        return (
          <PageNfcStorageLogic
            setPages={setPages}
            pages={pages}
            setTotalPages={setTotalPages}
          />
        );
      case "MANUAL_VERIFICATION":
        return (
          <PageManualVerification
            setPages={setPages}
            pages={pages}
            setTotalPages={setTotalPages}
          />
        );
      case "IDIN_VERIFICATION":
        return (
          <PageIdinVerification
            setPages={setPages}
            pages={pages}
            setTotalPages={setTotalPages}
          />
        );
      case "COMPLETED":
        return <div> Completed </div>;
      default:
        return <div>Page not found</div>;
    }
  };

  return (
    <Box h={"100%"}>
      <WizardHeader
        defaultTotalPages={DEFAULT_TOTAL_PAGES}
        setPages={setPages}
        setTotalPages={setTotalPages}
        pages={pages}
        totalPages={totalPages}
        closeModal={closeModal}
        currentWizardStatus={(currentWizard?.status ?? "NEW DRAFT") as string}
        verificationStatus={currentWizard?.verified ?? (false as boolean)}
      />
      {currentPageContent()}
    </Box>
  );
}
