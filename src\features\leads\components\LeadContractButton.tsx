import LeadContractWizard from "@/components/Wizards/LeadContract/LeadContractWizard";
import ButtonMain from "@/features/entity/components/Elements/ButtonMain/ButtonMain";
import { useEntityCreateMutation } from "@/features/entity/mutations";
import { useEntityListQuery } from "@/features/entity/queries";
import { type Schemas } from "@/types";
import { Modal } from "@mantine/core";
import { notifications } from "@mantine/notifications";
import { IconFileSymlink } from "@tabler/icons-react";
import { useTranslation } from "react-i18next";

interface LeadContractButtonProps {
  lead: Schemas["LeadRetrieveDto"];
  leadId: string;
  opened: boolean;
  activeWizard: string | null;
  setActiveWizard: (wizardId: string | null) => void;
  close: () => void;
  open: () => void;
}

export default function LeadContractButton({
  lead,
  leadId,
  opened,
  activeWizard,
  setActiveWizard,
  close,
  open,
}: LeadContractButtonProps) {
  const { t } = useTranslation("features");

  const { data, isLoading } = useEntityListQuery<
    Schemas["ContractRetrieveDtoPagedList"]
  >({
    resourcePath: `/api/Contracts`,
    queryKey: "Contract",
    params: {
      filter: `leadId == ${leadId}`,
    },
  });

  const { mutate: createContract } = useEntityCreateMutation<
    Schemas["Contract"],
    Schemas["ContractCreateDto"]
  >({ resourcePath: "/api/Contracts", queryKey: "Contract" });

  function createDraftContract() {
    const contractObject: Schemas["ContractCreateDto"] = {
      status: "Draft",
      leadId: leadId,
    };

    createContract(contractObject, {
      onSuccess: () => {
        notifications.show({
          color: "green",
          title: t("notifications.createSuccessTitle"),
          message: t("notifications.createSuccessMessage"),
        });
        open();
      },
    });
  }

  const handleOpen = () => {
    if (activeWizard === null) {
      if (data?.data?.length === 0) {
        createDraftContract();
      } else {
        open();
      }
    }
  };

  const handleClose = () => {
    setActiveWizard(null);
    close();
  };

  return (
    <>
      <ButtonMain
        label={t("leads.contractWizard")}
        loading={isLoading}
        icon={<IconFileSymlink size={18} />}
        onClick={handleOpen}
      />
      <Modal
        opened={opened}
        withCloseButton={false}
        onClose={handleClose}
        fullScreen
        centered
      >
        <LeadContractWizard
          closeModal={handleClose}
          lead={lead}
          activeWizard={activeWizard}
        />
      </Modal>
    </>
  );
}
