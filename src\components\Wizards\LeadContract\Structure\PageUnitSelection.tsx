import { type Schemas } from "@/types";
import { type PageProps } from "../../Common/Header/WizardHeader";
import { type PageName } from "../LeadContractWizard";
import { useTranslation } from "react-i18next";
import {
  Box,
  Button,
  Center,
  Flex,
  Loader,
  Paper,
  ScrollArea,
  Stack,
  Text,
} from "@mantine/core";
import { IconCheck, IconPencil, IconPlus } from "@tabler/icons-react";
import { useEntityListQuery } from "@/features/entity/queries";
import { UnitBox } from "../Common/UnitBox";

interface PageUnitSelectionProps extends PageProps<PageName> {
  lead?: Schemas["LeadRetrieveDto"];
  currentWizard?: Schemas["ContractRetrieveDto"];
}

export default function PageUnitSelection({
  lead,
  setPages,
  pages,
  currentWizard,
}: PageUnitSelectionProps) {
  const { t } = useTranslation("features");
  console.log(currentWizard);
  const { data, isLoading } = useEntityListQuery<
    Schemas["AdvisedUnitRetrieveDtoPagedList"]
  >({
    resourcePath: "/api/AdvisedUnits",
    queryKey: `units`,
    params: {
      filter: `businessUnitId == ${lead?.businessUnitId}`,
      orderBy: "code",
      desc: false,
    },
  });

  const units: Schemas["AdvisedUnitRetrieveDto"][] = data?.data ?? [];

  return (
    <Box>
      <Center mt={20}>
        <Flex
          direction={{ lg: "row", base: "column" }}
          w={"70%"}
          justify={"space-between"}
          gap={12}
        >
          <Paper
            flex={3}
            pt={8}
            pl={8}
            h={"68vh"}
            style={{ borderRadius: 8, border: "1px solid" }}
          >
            <Flex h={"90%"} direction={"column"} gap={8} justify={"start"}>
              <Text fz={18} fw={700} ml={16}>
                {t("Selected Units")}
              </Text>
              {isLoading && (
                <Center h={"80%"}>
                  <Loader size={"xl"} />
                </Center>
              )}
              {!isLoading && units.length > 0 && (
                <ScrollArea.Autosize mah={"56vh"} type="always" scrollbars="y">
                  <Stack gap="md" mr={"1vw"}>
                    {units.map((unit) => (
                      <UnitBox
                        key={unit.id ?? unit.id}
                        unit={unit}
                        editTagButton={
                          <Button
                            variant="outline"
                            size="xs"
                            leftSection={<IconPencil size={18} />}
                            onClick={() => setPages([...pages, "ACCESSORIES"])}
                          >
                            Edit tags
                          </Button>
                        }
                      />
                    ))}
                  </Stack>
                </ScrollArea.Autosize>
              )}
            </Flex>
            <Box mt={12} mr={"1vw"}>
              <Button
                w={"100%"}
                variant="outline"
                leftSection={<IconPlus />}
                onClick={() => setPages([...pages, "UNIT_SEARCH"])}
              >
                {t("Add Unit")}
              </Button>
            </Box>
          </Paper>
          <Paper
            flex={1}
            pt={8}
            pl={8}
            style={{ borderRadius: 8, border: "1px solid" }}
          >
            <Flex direction={"column"} h={"50vh"} gap={8} justify={"start"}>
              <Text fz={18} fw={700} ml={16}>
                {t("Summary")}
              </Text>
              {isLoading && (
                <Center h={"80%"}>
                  <Loader size={"xl"} />
                </Center>
              )}
              {!isLoading && units.length > 0 && (
                <ScrollArea.Autosize mah={"46vh"} type="always" scrollbars="y">
                  <Stack gap="xs" mr={"1vw"}>
                    {units.map((unit) => (
                      <Box
                        key={unit.id ?? unit.id}
                        style={{ borderRadius: 8, border: "1px solid" }}
                        pl={12}
                        pt={4}
                        pb={4}
                        pr={12}
                      >
                        <Flex direction="column" gap={2}>
                          <Flex direction="row" justify="space-between">
                            <Text fz={14} fw={400} c={"#282828"}>
                              {unit.unit?.unitCode}
                            </Text>
                            <Text fz={14} fw={400} c={"#282828"}>
                              {unit.pricePerMonth}
                            </Text>
                          </Flex>
                          <Flex direction="row" justify="space-between">
                            <Text fz={14} fw={400} c={"#282828"}>
                              {t("Accessories")}
                            </Text>
                            <Text fz={14} fw={400} c={"#282828"}>
                              {unit.unit?.minPrice}
                            </Text>
                          </Flex>
                          <Flex direction="row" justify="space-between">
                            <Text fz={14} fw={400} c={"#282828"}>
                              {t("Deposit")}
                            </Text>
                            <Text fz={14} fw={400} c={"#282828"}>
                              {unit.pricePerMonth}
                            </Text>
                          </Flex>
                        </Flex>
                      </Box>
                    ))}
                  </Stack>
                </ScrollArea.Autosize>
              )}
            </Flex>
            <Box
              mr={"1vw"}
              mt={12}
              p={16}
              style={{ border: "1px solid", borderRadius: 8 }}
            >
              <Flex direction={"column"} gap={8}>
                <Flex direction={"row"} justify={"space-between"} gap={8}>
                  <Text fz={14} fw={400} c={"#282828"}>
                    {t("Taxes (btw)")}
                  </Text>
                  <Text fz={14} fw={400} c={"#282828"}>
                    {t("79.8")}
                  </Text>
                </Flex>
                <Flex direction={"row"} justify={"space-between"} gap={8}>
                  <Text fz={18} fw={600} c={"#282828"}>
                    {t("Total")}
                  </Text>
                  <Text fz={18} fw={600} c={"#282828"}>
                    {t("380")}
                  </Text>
                </Flex>
              </Flex>
            </Box>
            <Box mr={"1vw"} mt={6}>
              <Button
                w={"100%"}
                variant="primary"
                leftSection={<IconCheck />}
                onClick={() => setPages([...pages, "DISCOUNT"])}
              >
                {t("Confirm Selected Units")}
              </Button>
            </Box>
          </Paper>
        </Flex>
      </Center>
    </Box>
  );
}
