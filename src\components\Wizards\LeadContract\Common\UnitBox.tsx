// UnitBox.tsx
import { type Schemas } from "@/types"; // Ensure this path is correct for your project
import {
  Badge,
  Box,
  Flex,
  Paper,
  Text,
  Group,
  Grid,
  Center,
} from "@mantine/core";
import { IconX } from "@tabler/icons-react";
import { useTranslation } from "react-i18next";

const formatDate = (dateString?: string | null): string => {
  if (!dateString) return "N/A";
  try {
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return dateString;
    const day = String(date.getDate()).padStart(2, "0");
    const month = String(date.getMonth() + 1).padStart(2, "0");
    const year = date.getFullYear();
    return `${day}-${month}-${year}`;
  } catch (error) {
    console.error("Error formatting date:", dateString, error);
    return dateString;
  }
};

const formatPrice = (
  price?: number | null,
  currencySymbol?: string | null,
): string => {
  if (price == null || currencySymbol == null) return "N/A";
  return `${currencySymbol}${price.toFixed(2).replace(".", ",")} p/m`;
};

interface UnitBoxProps {
  editTagButton?: React.ReactNode;
  unit: Schemas["AdvisedUnitRetrieveDto"] & {
    id?: string | number; // Ensure a unique ID for keys and actions
    code?: string;
    unitType?: { name: string } | string;
    unitSize?: { name: string } | string; // Or perhaps sizeValue + sizeUnit
    location?: { name: string } | string;
    expectedMoveInDate?: string | null;
    price?: number | null;
    currency?: { symbol: string } | string; // e.g., "€" or { symbol: "€" }
    status?: string | null;
    accessories?: ({ name: string } | string)[] | null;
  };
}

interface UnitInfoItemProps {
  label: string;
  value: React.ReactNode;
}

const UnitInfoItem: React.FC<UnitInfoItemProps> = ({ label, value }) => (
  <Box>
    <Text size="xs" c="dimmed">
      {label}
    </Text>
    {typeof value === "string" ? (
      <Text fw={500} lineClamp={1}>
        {value}
      </Text>
    ) : (
      value
    )}
  </Box>
);

export function UnitBox({ unit, editTagButton }: UnitBoxProps) {
  const { t } = useTranslation("features");

  const unitNumber = unit.code ?? "N/A";

  const getUnitTypeName = () => {
    if (typeof unit.unitType === "string" && unit.unitType)
      return unit.unitType;
    return unit.unitType ?? t("Unit type");
  };

  const getUnitSizeName = () => {
    if (typeof unit.unitSize === "string" && unit.unitSize)
      return unit.unitSize;
    return unit.unitSize ?? t("Unit size");
  };

  const getLocationName = () => {
    if (typeof unit.location === "string" && unit.location)
      return unit.location;
    return unit.location ?? t("Name location");
  };

  const getPriceDisplay = () => {
    const symbol =
      typeof unit.currency === "string" ? unit.currency : unit.currency?.symbol;
    return formatPrice(unit.price, symbol ?? "€");
  };

  const getAccessoriesDisplay = () => {
    if (!unit.accessories || unit.accessories.length === 0) {
      return [t("Name of accessory")];
    }
    return unit.accessories
      .map((acc) => (typeof acc === "string" ? acc : acc.name))
      .filter(Boolean);
  };

  const unitType = getUnitTypeName() as string;
  const unitSize = getUnitSizeName() as string;
  const location = getLocationName() as string;
  const moveInDate = formatDate(unit.expectedMoveInDate);
  const price = getPriceDisplay();
  const status = unit.status ?? t("Available");
  const accessories = getAccessoriesDisplay();

  const getStatusColor = (statusValue: string) => {
    switch (statusValue?.toLowerCase()) {
      case "available":
      case t("available").toLowerCase():
        return "green";
      case "reserved":
      case t("reserved").toLowerCase():
        return "orange";
      case "occupied":
      case t("occupied").toLowerCase():
        return "red";
      default:
        return "gray";
    }
  };

  return (
    <Paper
      shadow="sm"
      radius="md"
      withBorder
      style={{ border: "1px solid #DFDFDF" }}
    >
      <Flex
        style={{
          border: "1px solid #DFDFDF",
          borderTopRightRadius: 8,
          borderTopLeftRadius: 8,
        }}
        justify="space-between"
        p={8}
        align="center"
        mb="xs"
        bg={"#F6F6F6"}
      >
        <Text fw={700} size="lg">
          {t("Unit#", "Unit#")}
          {unitNumber}
        </Text>
        <Flex gap={24} align="center">
          {editTagButton}
          <Center>
            <IconX color="red" size={16} />
          </Center>
        </Flex>
      </Flex>
      <Box pl={16} pr={16} pb={16}>
        <Grid gutter="2">
          <Grid.Col span={{ base: 2, xs: 2, sm: 2 }}>
            <UnitInfoItem label={t("Type")} value={unitType} />
          </Grid.Col>
          <Grid.Col span={{ base: 2, xs: 2, sm: 2 }}>
            <UnitInfoItem label={t("Size")} value={unitSize} />
          </Grid.Col>
          <Grid.Col span={{ base: 2, xs: 2, sm: 2 }}>
            <UnitInfoItem label={t("Location")} value={location} />
          </Grid.Col>
          <Grid.Col span={{ base: 2, xs: 2, sm: 2 }}>
            <UnitInfoItem label={t("Move in date")} value={moveInDate} />
          </Grid.Col>
          <Grid.Col span={{ base: 2, xs: 2, sm: 2 }}>
            <UnitInfoItem label={t("Price")} value={price} />
          </Grid.Col>
          <Grid.Col span={{ base: 2, xs: 2, sm: 2 }}>
            <UnitInfoItem
              label={t("Status")}
              value={
                <Badge
                  color={getStatusColor(status)}
                  variant="light"
                  mt={4}
                  fullWidth
                  style={{ textTransform: "capitalize" }}
                >
                  {status}
                </Badge>
              }
            />
          </Grid.Col>
        </Grid>

        {accessories && accessories.length > 0 && (
          <Box mt="xs">
            <Text size="xs" c="dimmed" mb={4}>
              {t("Tags")}
            </Text>
            <Group gap="xs" mt={4}>
              {accessories.map((tag, index) => (
                <Badge key={index} variant="light" color="blue" radius="xl">
                  {tag}
                </Badge>
              ))}
            </Group>
          </Box>
        )}
      </Box>
    </Paper>
  );
}
