import { type Schemas } from "@/types";
import { type PageProps } from "../../Common/Header/WizardHeader";
import { type PageName } from "../LeadContractWizard";
import { useTranslation } from "react-i18next";
import {
  Button,
  Center,
  Paper,
  Stack,
  Text,
  TextInput,
  Group,
  Select,
  Checkbox,
  Table,
  Flex,
  Loader,
} from "@mantine/core";
import { IconPlus, IconSearch, IconChevronDown } from "@tabler/icons-react";
import { useState } from "react";
import { useEntityListQuery } from "@/features/entity/queries";

interface PageUnitBrowseProps extends PageProps<PageName> {
  lead?: Schemas["LeadRetrieveDto"];
}

interface FilterState {
  unitType: string;
  unitSize: string;
  price: string;
  availability: string;
  location: string;
}

export default function PageUnitBrowse({
  lead: _lead,
  setPages,
  pages,
}: PageUnitBrowseProps) {
  const { t } = useTranslation("features");
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedUnits, setSelectedUnits] = useState<string[]>([]);

  const { data, isLoading } = useEntityListQuery<
    Schemas["UnitRetrieveDtoPagedList"]
  >({
    resourcePath: "/api/Units",
    queryKey: `units`,
    params: {
      orderBy: "code",
      desc: false,
    },
  });

  const mockUnits = data?.data ?? [];
  const [filters, setFilters] = useState<FilterState>({
    unitType: "1",
    unitSize: "",
    price: "",
    availability: "",
    location: "",
  });
  if (isLoading) {
    return (
      <Center>
        <Loader />
      </Center>
    );
  }
  console.log(data);
  // Mock data for filters
  const unitTypeOptions = [
    { value: "1", label: "Type of unit (1)" },
    { value: "regular", label: "Regular unit" },
    { value: "climate", label: "Climate controlled" },
  ];

  const unitSizeOptions = [
    { value: "", label: "Unit size" },
    { value: "5m3", label: "5m3" },
    { value: "10m3", label: "10m3" },
    { value: "13m3", label: "13m3" },
  ];

  const priceOptions = [
    { value: "", label: "Price" },
    { value: "0-100", label: "€0 - €100" },
    { value: "100-200", label: "€100 - €200" },
    { value: "200+", label: "€200+" },
  ];

  const availabilityOptions = [
    { value: "", label: "Availability" },
    { value: "available", label: "Available" },
    { value: "reserved", label: "Reserved" },
  ];

  const locationOptions = [
    { value: "", label: "Location" },
    { value: "ground", label: "Ground floor" },
    { value: "first", label: "First floor" },
  ];

  const handleUnitToggle = (unitId: string) => {
    setSelectedUnits((prev) =>
      prev.includes(unitId)
        ? prev.filter((id) => id !== unitId)
        : [...prev, unitId],
    );
  };

  const handleAddSelectedUnits = () => {
    console.log("Adding selected units:", selectedUnits);
    // Navigate back to unit selection page
    setPages(pages.slice(0, -2)); // Go back 2 pages to skip the search page
  };

  return (
    <Center>
      <Paper w={"70vw"} p="xs" withBorder>
        <Stack gap={4}>
          <TextInput
            placeholder={t("Search")}
            leftSection={<IconSearch size={16} />}
            value={searchTerm}
            onChange={(event) => setSearchTerm(event.currentTarget.value)}
            size="md"
          />

          <Flex gap="md">
            <Select
              data={unitTypeOptions}
              value={filters.unitType}
              onChange={(value) =>
                setFilters((prev) => ({ ...prev, unitType: value || "" }))
              }
              leftSection={<IconChevronDown size={16} />}
              size="sm"
            />
            <Select
              data={unitSizeOptions}
              value={filters.unitSize}
              onChange={(value) =>
                setFilters((prev) => ({ ...prev, unitSize: value || "" }))
              }
              placeholder="Unit size"
              size="sm"
            />
            <Select
              data={priceOptions}
              value={filters.price}
              onChange={(value) =>
                setFilters((prev) => ({ ...prev, price: value || "" }))
              }
              placeholder="Price"
              size="sm"
            />
            <Select
              data={availabilityOptions}
              value={filters.availability}
              onChange={(value) =>
                setFilters((prev) => ({ ...prev, availability: value || "" }))
              }
              placeholder="Availability"
              size="sm"
            />
            <Select
              data={locationOptions}
              value={filters.location}
              onChange={(value) =>
                setFilters((prev) => ({ ...prev, location: value || "" }))
              }
              placeholder="Location"
              size="sm"
            />
          </Flex>

          <Table highlightOnHover>
            <Table.Thead>
              <Table.Tr>
                <Table.Th></Table.Th>
                <Table.Th>{t("Unit")}</Table.Th>
                <Table.Th>{t("Business Unit")}</Table.Th>
                <Table.Th>{t("Type")}</Table.Th>
                <Table.Th>{t("Size")}</Table.Th>
                <Table.Th>{t("Price")}</Table.Th>
              </Table.Tr>
            </Table.Thead>
            <Table.Tbody>
              {mockUnits.map((unit) => (
                <Table.Tr
                  key={unit.id}
                  style={{
                    backgroundColor: selectedUnits.includes(unit.id!)
                      ? "rgba(0, 123, 255, 0.1)"
                      : "transparent",
                  }}
                >
                  <Table.Td>
                    <Checkbox
                      checked={selectedUnits.includes(unit.id!)}
                      onChange={() => handleUnitToggle(unit.id!)}
                    />
                  </Table.Td>
                  <Table.Td>
                    <Text fw={500}>{unit.unitCode}</Text>
                  </Table.Td>
                  <Table.Td>
                    <Text>{unit.businessUnit?.name}</Text>
                  </Table.Td>
                  <Table.Td>
                    <Text>{unit.unitType?.name}</Text>
                  </Table.Td>
                  <Table.Td>
                    <Text>{unit.status}</Text>
                  </Table.Td>
                  <Table.Td>
                    <Text>{unit.area}</Text>
                  </Table.Td>
                </Table.Tr>
              ))}
            </Table.Tbody>
          </Table>

          <Group justify="flex-end" mt="lg">
            <Button
              leftSection={<IconPlus size={16} />}
              onClick={handleAddSelectedUnits}
              disabled={selectedUnits.length === 0}
            >
              {t("Add selected units")}
            </Button>
          </Group>
        </Stack>
      </Paper>
    </Center>
  );
}
