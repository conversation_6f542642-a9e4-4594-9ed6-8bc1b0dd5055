import { useState } from "react";
import { type PageProps } from "../../Common/Header/WizardHeader";
import { type PageName } from "../LeadContractWizard";
import { type Schemas } from "@/types";
import { useTranslation } from "react-i18next";
import {
  Box,
  Button,
  Center,
  Stack,
  Text,
  Select,
  Paper,
  Loader,
} from "@mantine/core";
import { IconHome } from "@tabler/icons-react";
import { DateInput } from "@mantine/dates";
import { useEntityListQuery } from "@/features/entity/queries";

interface PageUnitSearchProps extends PageProps<PageName> {
  lead?: Schemas["LeadRetrieveDto"];
}

interface UnitSearchCriteria {
  businessUnit: string;
  unitType: string;
  unitSize: string;
  availableTags: string;
  availabilityDate: Date | null;
}

export default function PageUnitSearch({
  lead,
  setPages,
  pages,
}: PageUnitSearchProps) {
  const { t } = useTranslation("features");

  const [searchCriteria, setSearchCriteria] = useState<UnitSearchCriteria>({
    businessUnit: lead?.businessUnitId?.toString() || "",
    unitType: "",
    unitSize: "",
    availableTags: "",
    availabilityDate: null,
  });

  const { data: businessUnitsResponse, isLoading: isLoadingBU } =
    useEntityListQuery<Schemas["BusinessUnitRetrieveDtoPagedList"]>({
      resourcePath: "/api/BusinessUnits",
      queryKey: `businessUnits`,
      params: {
        pageSize: 1000,
        orderBy: "name",
        desc: false,
      },
    });

  const { data: unitTypesResponse, isLoading: isLoadingUnitTypes } =
    useEntityListQuery<Schemas["UnitTypeRetrieveDtoPagedList"]>({
      resourcePath: "/api/UnitTypes",
      queryKey: `unitTypes`,
      params: {
        pageSize: 1000,
        orderBy: "name",
        desc: false,
      },
    });

  const { data: tagResponse, isLoading: isLoadingTags } = useEntityListQuery<
    Schemas["TagRetrieveDtoPagedList"]
  >({
    resourcePath: "/api/Tags",
    queryKey: `tags`,
    params: {
      pageSize: 1000,
      orderBy: "name",
      desc: false,
    },
  });

  const isLoading = isLoadingBU || isLoadingUnitTypes || isLoadingTags;
  if (isLoading) {
    return (
      <Center>
        <Loader size="xl" />
      </Center>
    );
  }

  const businessUnitsRaw = businessUnitsResponse?.data ?? [];
  const businessUnits = businessUnitsRaw.map((businessUnit) => {
    return {
      label: `${businessUnit?.code ?? ""}`,
      value: businessUnit?.id ?? "",
    };
  });

  const unitTypesRaw = unitTypesResponse?.data ?? [];
  const unitTypes = unitTypesRaw.map((unitType) => {
    return {
      label: `${unitType?.name ?? ""}`,
      value: unitType?.id ?? "",
    };
  });

  const tagsRaw = tagResponse?.data ?? [];
  const availableTags = tagsRaw.map((availableTag) => {
    return {
      label: `${availableTag?.name ?? ""}`,
      value: availableTag?.id ?? "",
    };
  });

  const handleCheckAvailability = () => {
    setPages([...pages, "UNIT_BROWSE"]);
  };

  return (
    <Center>
      <Paper w={"30vw"}>
        <Stack gap="xs">
          <Box>
            <Text fz={14} fw={500} c="dimmed">
              {t("Business unit")}
            </Text>
            <Select
              placeholder={t("Select business unit")}
              data={businessUnits}
              value={searchCriteria.businessUnit}
              searchable
              clearable
              onChange={(value) =>
                setSearchCriteria((prev) => ({
                  ...prev,
                  businessUnit: value || "",
                }))
              }
              size="md"
            />
          </Box>

          <Box>
            <Text fz={14} fw={500} c="dimmed">
              {t("Unit type")}
            </Text>
            <Select
              placeholder={t("Select unit type")}
              data={unitTypes}
              value={searchCriteria.unitType}
              onChange={(value) =>
                setSearchCriteria((prev) => ({
                  ...prev,
                  unitType: value || "",
                }))
              }
              size="md"
            />
          </Box>
          <Box>
            <Text fz={14} fw={500} c="dimmed">
              {t("Available tags")}
            </Text>
            <Select
              placeholder={t("Select available tags")}
              data={availableTags}
              value={searchCriteria.availableTags}
              onChange={(value) =>
                setSearchCriteria((prev) => ({
                  ...prev,
                  availableTags: value || "",
                }))
              }
              size="md"
            />
          </Box>

          <Box>
            <Text fz={14} fw={500} c="dimmed">
              {t("Availability date")}
            </Text>
            <DateInput
              placeholder={t("Select availability date")}
              value={searchCriteria.availabilityDate}
              onChange={(value) =>
                setSearchCriteria((prev) => ({
                  ...prev,
                  availabilityDate: value,
                }))
              }
              size="md"
              valueFormat="DD-MM-YYYY"
            />
          </Box>

          <Button
            leftSection={<IconHome size={20} />}
            onClick={handleCheckAvailability}
            size="md"
            mt="md"
            variant="light"
            fullWidth
          >
            {t("Check unit availability")}
          </Button>
        </Stack>
      </Paper>
    </Center>
  );
}
